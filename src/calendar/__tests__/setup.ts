import { jest } from '@jest/globals';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

// Mock Firebase Admin SDK
jest.mock('firebase-admin', () => ({
  apps: [],
  initializeApp: jest.fn(),
  credential: {
    cert: jest.fn(),
  },
  firestore: jest.fn(() => ({
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        set: jest.fn(() => Promise.resolve()),
        get: jest.fn(() =>
          Promise.resolve({
            exists: false,
            data: () => null,
          })
        ),
        update: jest.fn(() => Promise.resolve()),
        delete: jest.fn(() => Promise.resolve()),
      })),
      orderBy: jest.fn(function () {
        return this;
      }),
      limit: jest.fn(function () {
        return this;
      }),
      get: jest.fn(() =>
        Promise.resolve({
          empty: true,
          docs: [],
          size: 0,
          forEach: jest.fn(),
        })
      ),
    })),
  })),
}));

// Mock environment variables for tests
process.env.NODE_ENV = 'test';
process.env.FIREBASE_PROJECT_ID = 'test-project';
process.env.FIREBASE_CLIENT_EMAIL = '<EMAIL>';
process.env.FIREBASE_PRIVATE_KEY =
  '-----BEGIN PRIVATE KEY-----\ntest\n-----END PRIVATE KEY-----';
process.env.COLLECTION = 'test-collection';
process.env.DOC_ID = 'test-doc';

// Suppress console logs during tests unless explicitly needed
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterEach(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test utilities
global.testUtils = {
  generateMockTokens: () => ({
    accessToken: 'mock_access_token_' + Math.random().toString(36).substr(2, 9),
    refreshToken:
      'mock_refresh_token_' + Math.random().toString(36).substr(2, 9),
    expiresIn: Date.now() + 3600000, // 1 hour from now
  }),

  generateMockEncryptionKey: () => {
    return crypto.randomBytes(32).toString('base64');
  },

  createTempDir: () => {
    return fs.mkdtempSync(path.join(__dirname, 'temp-test-'));
  },

  cleanupTempDir: (dir: string) => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
    }
  },
};

// Extend Jest matchers for better testing
expect.extend({
  toBeValidEncryptedData(received) {
    const pass =
      typeof received === 'object' &&
      received !== null &&
      typeof received.encryptedData === 'string' &&
      typeof received.iv === 'string' &&
      typeof received.authTag === 'string' &&
      typeof received.algorithm === 'string' &&
      typeof received.timestamp === 'number';

    if (pass) {
      return {
        message: () =>
          `expected ${JSON.stringify(received)} not to be valid encrypted data`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${JSON.stringify(received)} to be valid encrypted data`,
        pass: false,
      };
    }
  },

  toBeValidTokenData(received) {
    const pass =
      typeof received === 'object' &&
      received !== null &&
      typeof received.accessToken === 'string' &&
      typeof received.refreshToken === 'string' &&
      typeof received.expiresIn === 'number';

    if (pass) {
      return {
        message: () =>
          `expected ${JSON.stringify(received)} not to be valid token data`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${JSON.stringify(received)} to be valid token data`,
        pass: false,
      };
    }
  },
});

// Type declarations for custom matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidEncryptedData(): R;
      toBeValidTokenData(): R;
    }
  }

  var testUtils: {
    generateMockTokens: () => {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    };
    generateMockEncryptionKey: () => string;
    createTempDir: () => string;
    cleanupTempDir: (dir: string) => void;
  };
}
